#!/usr/bin/env python3
"""
Test script to demonstrate LangChain retriever functionality with FAISS vector store.

This script shows how to:
1. Create a FAISS vector store from sample documents
2. Create different types of LangChain retrievers
3. Compare retrieval methods
4. Use advanced retriever configurations
"""

import os
import sys
from pathlib import Path
from typing import List
from langchain_core.documents import Document
from langchain_ollama.embeddings import OllamaEmbeddings

# Add the current directory to Python path to import from non_agent.py
sys.path.append(str(Path(__file__).parent))

from non_agent import (
    create_vector_store,
    create_retriever_from_vector_store,
    create_advanced_retriever,
    create_bm25_retriever,
    retrieve_context_with_langchain_retriever,
    retrieve_context_with_bm25_retriever,
    retrieve_context_hybrid,
    retrieve_context_from_vector_db,
    compare_retrieval_methods,
    cleanup_vector_store
)

# Configuration
EMBEDDING_NAME = 'mxbai-embed-large:latest'

def create_sample_documents() -> List[Document]:
    """Create sample insurance policy documents for testing."""
    sample_docs = [
        Document(
            page_content="Applied Aviation Commercial General Liability insurance provides coverage for aviation-related businesses including aircraft operators, airports, and aviation service providers.",
            metadata={"source": "aviation_policy.pdf", "page_number": 1}
        ),
        Document(
            page_content="Applied Entertainment Sports General Liability offers protection for entertainment venues, sports facilities, and event organizers against third-party claims.",
            metadata={"source": "entertainment_policy.pdf", "page_number": 1}
        ),
        Document(
            page_content="Applied Financial Lines Professional Liability insurance covers financial institutions and professionals against errors and omissions claims.",
            metadata={"source": "financial_policy.pdf", "page_number": 1}
        ),
        Document(
            page_content="Commercial Property insurance protects business property including buildings, equipment, and inventory against various perils.",
            metadata={"source": "property_policy.pdf", "page_number": 1}
        ),
        Document(
            page_content="Applied Specialty Underwriters Commercial Excess Liability provides additional liability coverage above primary insurance limits.",
            metadata={"source": "excess_policy.pdf", "page_number": 1}
        )
    ]
    return sample_docs

def test_basic_retriever():
    """Test basic LangChain retriever functionality."""
    print("\n" + "="*60)
    print("TESTING BASIC LANGCHAIN RETRIEVER")
    print("="*60)
    
    # Initialize embeddings
    print("Initializing embeddings...")
    embeddings = OllamaEmbeddings(model=EMBEDDING_NAME)
    
    # Create sample documents
    documents = create_sample_documents()
    print(f"Created {len(documents)} sample documents")
    
    # Create vector store
    print("Creating FAISS vector store...")
    vector_store = create_vector_store(documents, embeddings)
    
    # Create basic retriever
    print("Creating basic LangChain retriever...")
    retriever = create_retriever_from_vector_store(vector_store)
    
    # Test queries
    test_queries = [
        "aviation insurance coverage",
        "entertainment liability",
        "financial professional errors"
    ]
    
    print("\nTesting retrieval with different queries:")
    for query in test_queries:
        print(f"\nQuery: '{query}'")
        try:
            results = retriever.invoke(query)
            print(f"Found {len(results)} documents:")
            for i, doc in enumerate(results, 1):
                source = doc.metadata.get('source', 'Unknown')
                print(f"  {i}. {source}: {doc.page_content[:100]}...")
        except Exception as e:
            print(f"Error: {e}")

def test_advanced_retrievers():
    """Test advanced retriever configurations."""
    print("\n" + "="*60)
    print("TESTING ADVANCED RETRIEVER CONFIGURATIONS")
    print("="*60)
    
    # Initialize embeddings
    embeddings = OllamaEmbeddings(model=EMBEDDING_NAME)
    documents = create_sample_documents()
    vector_store = create_vector_store(documents, embeddings)
    
    # Test different retriever types
    retriever_configs = [
        {"search_type": "similarity", "k": 2},
        {"search_type": "mmr", "k": 2, "fetch_k": 4, "lambda_mult": 0.5},
        {"search_type": "similarity_score_threshold", "k": 3, "score_threshold": 0.3}
    ]
    
    query = "liability insurance coverage"
    
    for config in retriever_configs:
        print(f"\nTesting retriever with config: {config}")
        try:
            retriever = create_advanced_retriever(vector_store, **config)
            results = retriever.invoke(query)
            print(f"Found {len(results)} documents")
            for i, doc in enumerate(results, 1):
                source = doc.metadata.get('source', 'Unknown')
                print(f"  {i}. {source}")
        except Exception as e:
            print(f"Error with config {config}: {e}")

def test_bm25_retriever():
    """Test BM25 retriever functionality."""
    print("\n" + "="*60)
    print("TESTING BM25 RETRIEVER")
    print("="*60)

    # Create sample documents
    documents = create_sample_documents()
    print(f"Created {len(documents)} sample documents")

    # Create BM25 retriever
    print("Creating BM25 retriever...")
    bm25_retriever = create_bm25_retriever(documents, k=3)

    # Test queries
    test_queries = [
        "aviation insurance coverage",
        "entertainment liability",
        "financial professional errors",
        "commercial property protection"
    ]

    print("\nTesting BM25 retrieval with different queries:")
    for query in test_queries:
        print(f"\nQuery: '{query}'")
        try:
            results = bm25_retriever.invoke(query)
            print(f"Found {len(results)} documents:")
            for i, doc in enumerate(results, 1):
                source = doc.metadata.get('source', 'Unknown')
                print(f"  {i}. {source}: {doc.page_content[:100]}...")
        except Exception as e:
            print(f"Error: {e}")


def test_hybrid_retrieval():
    """Test hybrid retrieval combining vector and BM25."""
    print("\n" + "="*60)
    print("TESTING HYBRID RETRIEVAL (VECTOR + BM25)")
    print("="*60)

    # Set up global variables (simulating the main application)
    import non_agent

    # Initialize embeddings
    embeddings = OllamaEmbeddings(model=EMBEDDING_NAME)
    documents = create_sample_documents()

    # Create vector store, retriever, and BM25 retriever
    non_agent.current_vector_store = create_vector_store(documents, embeddings)
    non_agent.current_retriever = create_retriever_from_vector_store(non_agent.current_vector_store)
    non_agent.current_bm25_retriever = create_bm25_retriever(documents, k=2)

    # Test hybrid retrieval with different weight combinations
    query = "aviation commercial liability"
    weight_combinations = [
        (0.8, 0.2),  # Vector-heavy
        (0.5, 0.5),  # Balanced
        (0.3, 0.7),  # BM25-heavy
    ]

    for vector_weight, bm25_weight in weight_combinations:
        print(f"\nTesting hybrid retrieval with weights: Vector={vector_weight}, BM25={bm25_weight}")
        try:
            results = retrieve_context_hybrid(query, vector_weight, bm25_weight)
            print(f"Found {len(results)} documents:")
            for i, doc in enumerate(results, 1):
                source = doc.metadata.get('source', 'Unknown')
                method = doc.metadata.get('retrieval_method', 'unknown')
                print(f"  {i}. {source} (via {method})")
        except Exception as e:
            print(f"Error: {e}")


def test_retrieval_comparison():
    """Test comparison between different retrieval methods."""
    print("\n" + "="*60)
    print("TESTING RETRIEVAL METHOD COMPARISON")
    print("="*60)

    # Set up global variables (simulating the main application)
    import non_agent

    # Initialize embeddings
    embeddings = OllamaEmbeddings(model=EMBEDDING_NAME)
    documents = create_sample_documents()

    # Create all retrievers
    non_agent.current_vector_store = create_vector_store(documents, embeddings)
    non_agent.current_retriever = create_retriever_from_vector_store(non_agent.current_vector_store)
    non_agent.current_bm25_retriever = create_bm25_retriever(documents, k=2)

    # Test comparison
    query = "aviation commercial liability"
    print(f"Comparing retrieval methods for query: '{query}'")

    comparison = compare_retrieval_methods(query, top_k=2)

    print(f"\nComparison Results:")
    print(f"Direct vector store: {comparison['comparison']['direct_count']} documents")
    print(f"LangChain retriever: {comparison['comparison']['retriever_count']} documents")
    print(f"BM25 retriever: {comparison['comparison']['bm25_count']} documents")
    print(f"Vector methods agree: {comparison['comparison']['vector_methods_agree']}")
    print(f"All methods agree: {comparison['comparison']['all_methods_agree']}")

    # Show actual results
    print(f"\nDirect vector store results:")
    for i, doc in enumerate(comparison['direct_vector_store'], 1):
        source = doc.metadata.get('source', 'Unknown')
        print(f"  {i}. {source}")

    print(f"\nLangChain retriever results:")
    for i, doc in enumerate(comparison['langchain_retriever'], 1):
        source = doc.metadata.get('source', 'Unknown')
        print(f"  {i}. {source}")

    print(f"\nBM25 retriever results:")
    for i, doc in enumerate(comparison['bm25_retriever'], 1):
        source = doc.metadata.get('source', 'Unknown')
        print(f"  {i}. {source}")

def main():
    """Run all tests."""
    print("LangChain Retriever Test Suite")
    print("Testing FAISS vector store integration with LangChain retrievers and BM25")

    try:
        # Test basic retriever
        test_basic_retriever()

        # Test advanced retrievers
        test_advanced_retrievers()

        # Test BM25 retriever
        test_bm25_retriever()

        # Test hybrid retrieval
        test_hybrid_retrieval()

        # Test retrieval comparison
        test_retrieval_comparison()

        print("\n" + "="*60)
        print("ALL TESTS COMPLETED SUCCESSFULLY!")
        print("="*60)

    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # Cleanup
        cleanup_vector_store()
        print("\nCleanup completed.")

if __name__ == "__main__":
    main()
