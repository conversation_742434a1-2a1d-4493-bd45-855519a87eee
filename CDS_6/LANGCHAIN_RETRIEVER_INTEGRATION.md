# LangChain Retriever Integration with FAISS Vector Store and BM25

## Overview

The `non_agent.py` file has been enhanced to include comprehensive LangChain retriever functionality alongside the existing FAISS vector store implementation. This integration now includes vector-based retrieval, BM25 keyword-based retrieval, and hybrid approaches that combine both methods for optimal results.

## Key Enhancements

### 1. New Imports and Dependencies
```python
from langchain_core.retrievers import BaseRetriever
from langchain_core.vectorstores import VectorStoreRetriever
from langchain_community.retrievers import BM25Retriever
```

### 2. Global Variables
- Added `current_retriever` to track the active LangChain retriever instance
- Added `current_bm25_retriever` to track the BM25 retriever instance
- Maintains existing `current_vector_store` for backward compatibility

### 3. New Functions

#### `create_retriever_from_vector_store(vector_store, search_kwargs)`
Creates a basic LangChain retriever from a FAISS vector store.

**Parameters:**
- `vector_store`: FAISS vector store instance
- `search_kwargs`: Dictionary with search parameters (default: `{"k": top_k_value}`)

**Returns:** VectorStoreRetriever instance

#### `create_advanced_retriever(vector_store, search_type, **kwargs)`
Creates an advanced retriever with different search strategies.

**Search Types:**
- `"similarity"`: Standard similarity search
- `"similarity_score_threshold"`: Similarity with minimum score threshold
- `"mmr"`: Maximal Marginal Relevance (reduces redundancy)

**Parameters:**
- `search_type`: Type of search algorithm
- `k`: Number of documents to retrieve
- `score_threshold`: Minimum similarity score (for score threshold search)
- `fetch_k`: Number of documents to fetch before MMR filtering
- `lambda_mult`: Diversity parameter for MMR (0=diverse, 1=similar)

#### `retrieve_context_with_langchain_retriever(query)`
Uses the LangChain retriever interface to retrieve documents.

**Parameters:**
- `query`: Search query string

**Returns:** List of Document objects

#### `create_bm25_retriever(documents, k)`
Creates a BM25 retriever from documents for keyword-based search.

**Parameters:**
- `documents`: List of Document objects
- `k`: Number of documents to retrieve (default: top_k_value)

**Returns:** BM25Retriever instance

#### `retrieve_context_with_bm25_retriever(query)`
Uses the BM25 retriever interface to retrieve documents based on keyword matching.

**Parameters:**
- `query`: Search query string

**Returns:** List of Document objects

#### `retrieve_context_hybrid(query, vector_weight, bm25_weight)`
Combines vector similarity and BM25 retrieval for optimal results.

**Parameters:**
- `query`: Search query string
- `vector_weight`: Weight for vector retrieval (0.0 to 1.0, default: 0.7)
- `bm25_weight`: Weight for BM25 retrieval (0.0 to 1.0, default: 0.3)

**Returns:** List of Document objects (combined and deduplicated)

#### `compare_retrieval_methods(query, top_k)`
Compares results from direct vector store, LangChain retriever, and BM25 retriever.

**Returns:** Dictionary with comparison results and statistics

### 4. Enhanced Classification Function

The `classify_insurance_policy_chain_based` function now:
1. Creates both a FAISS vector store and LangChain retriever
2. Uses LangChain retriever as the primary retrieval method
3. Falls back to direct vector store method if retriever fails
4. Includes optional retrieval method comparison for validation

## Usage Examples

### Basic Usage
The system automatically creates and uses LangChain retrievers. No changes needed to existing code.

### Advanced Retriever Configuration
To use MMR (Maximal Marginal Relevance) retriever, uncomment and modify in the classification function:

```python
# Option 2: Advanced retriever with MMR (uncomment to use)
current_retriever = create_advanced_retriever(
    current_vector_store, 
    search_type="mmr", 
    k=top_k_value, 
    fetch_k=20, 
    lambda_mult=0.5
)
```

### Manual Retriever Creation
```python
# Create embeddings
embeddings = OllamaEmbeddings(model=EMBEDDING_NAME)

# Create vector store
vector_store = create_vector_store(documents, embeddings)

# Create basic retriever
retriever = create_retriever_from_vector_store(vector_store)

# Create advanced MMR retriever
mmr_retriever = create_advanced_retriever(
    vector_store, 
    search_type="mmr",
    k=5,
    fetch_k=20,
    lambda_mult=0.5
)

# Use retriever
results = retriever.invoke("your query here")
```

## Testing

A comprehensive test script `test_retriever.py` is provided to demonstrate:
1. Basic retriever functionality
2. Advanced retriever configurations
3. Retrieval method comparisons
4. Error handling and fallback mechanisms

Run the test:
```bash
python CDS_6/test_retriever.py
```

## Benefits of LangChain Retriever Integration

### 1. Standardization
- Uses LangChain's standard retriever interface
- Compatible with LangChain chains and agents
- Consistent API across different vector stores

### 2. Advanced Search Options
- **MMR (Maximal Marginal Relevance)**: Reduces redundancy in results
- **Score Thresholding**: Filters results by minimum similarity score
- **Configurable Parameters**: Fine-tune retrieval behavior

### 3. Flexibility
- Easy to switch between different retrieval strategies
- Backward compatibility with existing direct vector store methods
- Fallback mechanisms for robustness

### 4. Integration Ready
- Compatible with LangChain agents and tools
- Can be easily integrated into RAG (Retrieval-Augmented Generation) pipelines
- Supports LangChain's retrieval evaluation tools

## Configuration Options

### Search Types and Parameters

#### Similarity Search
```python
search_kwargs = {"k": 5}
```

#### Score Threshold Search
```python
search_kwargs = {
    "k": 5,
    "score_threshold": 0.5  # Only return docs with similarity >= 0.5
}
```

#### MMR Search
```python
search_kwargs = {
    "k": 5,           # Final number of documents
    "fetch_k": 20,    # Initial fetch size
    "lambda_mult": 0.5  # 0=diverse, 1=similar
}
```

## Backward Compatibility

All existing functionality remains unchanged:
- `retrieve_context_from_vector_db()` still works as before
- Direct vector store operations are preserved
- No breaking changes to existing code

## Error Handling

The implementation includes robust error handling:
- Automatic fallback from retriever to direct vector store methods
- Graceful degradation when retriever creation fails
- Comprehensive error logging and reporting

## Future Enhancements

The LangChain retriever integration opens possibilities for:
1. Integration with LangChain agents and tools
2. Advanced retrieval strategies (e.g., multi-query, contextual compression)
3. Retrieval evaluation and optimization
4. Integration with other vector stores through LangChain's unified interface
